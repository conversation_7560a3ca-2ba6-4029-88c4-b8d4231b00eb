#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

console.log('========================================');
console.log('  Event App Backend with Stable Tunnel');
console.log('========================================');
console.log('');
console.log('Starting backend server and tunnel...');
console.log('');
console.log('Backend will be available at:');
console.log('  Local: http://localhost:5000');
console.log('  Tunnel: https://eventapp-api-stable.loca.lt');
console.log('');
console.log('Press Ctrl+C to stop both server and tunnel');
console.log('');

// Start the backend server
const isWindows = process.platform === 'win32';
const npmCommand = isWindows ? 'npm.cmd' : 'npm';

const serverProcess = spawn(npmCommand, ['run', 'dev'], {
  stdio: 'inherit',
  cwd: __dirname,
  shell: isWindows
});

// Wait a moment for server to start
setTimeout(() => {
  // Start the tunnel
  const npxCommand = isWindows ? 'npx.cmd' : 'npx';

  const tunnelProcess = spawn(npxCommand, ['localtunnel', '--port', '5000', '--subdomain', 'eventapp-api-stable'], {
    stdio: 'inherit',
    cwd: __dirname,
    shell: isWindows
  });

  // Handle graceful shutdown
  process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down server and tunnel...');
    
    if (serverProcess) {
      serverProcess.kill('SIGINT');
    }
    
    if (tunnelProcess) {
      tunnelProcess.kill('SIGINT');
    }
    
    setTimeout(() => {
      process.exit(0);
    }, 1000);
  });

  tunnelProcess.on('close', (code) => {
    console.log(`Tunnel process exited with code ${code}`);
    if (serverProcess) {
      serverProcess.kill('SIGINT');
    }
    process.exit(code);
  });

}, 3000);

serverProcess.on('close', (code) => {
  console.log(`Server process exited with code ${code}`);
  process.exit(code);
});
