@echo off
echo ========================================
echo   Event App Backend with Stable Tunnel
echo ========================================
echo.
echo Starting backend server and tunnel...
echo.
echo Backend will be available at:
echo   Local: http://localhost:5000
echo   Tunnel: https://eventapp-api-stable.loca.lt
echo.
echo Press Ctrl+C to stop both server and tunnel
echo.

REM Start the backend server in background
start "Backend Server" cmd /c "npm run dev & pause"

REM Wait a moment for server to start
echo Waiting for server to start...
timeout /t 5 /nobreak >nul

REM Start the tunnel
echo Starting tunnel...
call npx localtunnel --port 5000 --subdomain eventapp-api-stable
