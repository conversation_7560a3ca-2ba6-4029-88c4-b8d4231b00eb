# Quick Start Guide - Stable Tunnel Setup

## 🚀 Simple 2-Step Setup

### Step 1: Start Backend Server
```bash
cd eventappbackend
npm run dev
```

### Step 2: Start Tunnel (in another terminal/command prompt)
```bash
cd eventappbackend
npx localtunnel --port 5000 --subdomain eventapp-api-stable
```

## ✅ Your Stable API URL
```
https://eventapp-api-stable.loca.lt/api
```

## 🧪 Test the Connection
```bash
cd eventapp
node test-api.js
```

Expected output:
```
API connection successful!
Number of services: 12
First service: Cake
```

## 📱 Start React Native App
```bash
cd eventapp
npx expo start
```

## 🔄 To Restart Everything
1. Press `Ctrl+C` in both terminals to stop
2. Run the 2 commands again
3. Same URL will be available immediately

## 💡 Pro Tips
- Keep both terminals open while developing
- The URL `https://eventapp-api-stable.loca.lt` stays the same every time
- Works from any device on any network
- No authentication required

## 🛠️ If Something Goes Wrong
1. Make sure port 5000 isn't used by another app
2. Check that both server and tunnel are running
3. Test the API with `node test-api.js`
4. Check the Services page in your React Native app
